:root {
  /* Primitive Color Tokens - Modern Purple/Blue Theme */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-gray-50: rgba(248, 250, 252, 1);
  --color-gray-100: rgba(241, 245, 249, 1);
  --color-gray-200: rgba(226, 232, 240, 1);
  --color-gray-300: rgba(203, 213, 225, 1);
  --color-gray-400: rgba(148, 163, 184, 1);
  --color-gray-500: rgba(100, 116, 139, 1);
  --color-gray-600: rgba(71, 85, 105, 1);
  --color-gray-700: rgba(51, 65, 85, 1);
  --color-gray-800: rgba(30, 41, 59, 1);
  --color-gray-900: rgba(15, 23, 42, 1);
  --color-purple-300: rgba(196, 181, 253, 1);
  --color-purple-400: rgba(167, 139, 250, 1);
  --color-purple-500: rgba(139, 92, 246, 1);
  --color-purple-600: rgba(124, 58, 237, 1);
  --color-purple-700: rgba(109, 40, 217, 1);
  --color-blue-400: rgba(96, 165, 250, 1);
  --color-blue-500: rgba(59, 130, 246, 1);
  --color-blue-600: rgba(37, 99, 235, 1);
  --color-indigo-500: rgba(99, 102, 241, 1);
  --color-indigo-600: rgba(79, 70, 229, 1);
  --color-red-400: rgba(248, 113, 113, 1);
  --color-red-500: rgba(239, 68, 68, 1);
  --color-orange-400: rgba(251, 146, 60, 1);
  --color-orange-500: rgba(249, 115, 22, 1);
  --color-green-400: rgba(74, 222, 128, 1);
  --color-green-500: rgba(34, 197, 94, 1);

  /* RGB versions for opacity control */
  --color-purple-500-rgb: 139, 92, 246;
  --color-purple-600-rgb: 124, 58, 237;
  --color-blue-500-rgb: 59, 130, 246;
  --color-blue-600-rgb: 37, 99, 235;
  --color-indigo-500-rgb: 99, 102, 241;
  --color-gray-900-rgb: 15, 23, 42;
  --color-gray-600-rgb: 71, 85, 105;
  --color-red-500-rgb: 239, 68, 68;
  --color-red-400-rgb: 248, 113, 113;
  --color-orange-500-rgb: 249, 115, 22;
  --color-orange-400-rgb: 251, 146, 60;
  --color-green-500-rgb: 34, 197, 94;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(139, 92, 246, 0.08); /* Light purple */
  --color-bg-2: rgba(99, 102, 241, 0.08); /* Light indigo */
  --color-bg-3: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-4: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-5: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-gray-50);
  --color-surface: var(--color-white);
  --color-text: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-600);
  --color-primary: var(--color-purple-500);
  --color-primary-hover: var(--color-purple-600);
  --color-primary-active: var(--color-purple-700);
  --color-secondary: rgba(var(--color-purple-500-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-purple-500-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-purple-500-rgb), 0.25);
  --color-border: rgba(var(--color-gray-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-white);
  --color-card-border: rgba(var(--color-gray-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-gray-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-green-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-blue-500);
  --color-focus-ring: rgba(var(--color-purple-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-gray-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23139' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 34, 197, 94;
  --color-error-rgb: 239, 68, 68;
  --color-warning-rgb: 249, 115, 22;
  --color-info-rgb: 59, 130, 246;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 148, 163, 184;
    --color-purple-400-rgb: 167, 139, 250;
    --color-purple-300-rgb: 196, 181, 253;
    --color-gray-300-rgb: 203, 213, 225;
    --color-gray-200-rgb: 226, 232, 240;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(139, 92, 246, 0.15); /* Dark purple */
    --color-bg-2: rgba(99, 102, 241, 0.15); /* Dark indigo */
    --color-bg-3: rgba(59, 130, 246, 0.15); /* Dark blue */
    --color-bg-4: rgba(34, 197, 94, 0.15); /* Dark green */
    --color-bg-5: rgba(239, 68, 68, 0.15); /* Dark red */
    --color-bg-6: rgba(249, 115, 22, 0.15); /* Dark orange */
    --color-bg-7: rgba(236, 72, 153, 0.15); /* Dark pink */
    --color-bg-8: rgba(6, 182, 212, 0.15); /* Dark cyan */

    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-gray-900);
    --color-surface: var(--color-gray-800);
    --color-text: var(--color-gray-100);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.8);
    --color-primary: var(--color-purple-400);
    --color-primary-hover: var(--color-purple-300);
    --color-primary-active: var(--color-purple-500);
    --color-secondary: rgba(var(--color-purple-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-purple-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-purple-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-green-400);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-blue-400);
    --color-focus-ring: rgba(var(--color-purple-400-rgb), 0.4);
    --color-btn-primary-text: var(--color-gray-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 74, 222, 128;
    --color-error-rgb: 248, 113, 113;
    --color-warning-rgb: 251, 146, 60;
    --color-info-rgb: 96, 165, 250;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
  
  /* Semantic Color Tokens (Dark Mode) */
  --color-background: var(--color-charcoal-700);
  --color-surface: var(--color-charcoal-800);
  --color-text: var(--color-gray-200);
  --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
  --color-primary: var(--color-teal-300);
  --color-primary-hover: var(--color-teal-400);
  --color-primary-active: var(--color-teal-800);
  --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
  --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
  --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
  --color-border: rgba(var(--color-gray-400-rgb), 0.3);
  --color-error: var(--color-red-400);
  --color-success: var(--color-teal-300);
  --color-warning: var(--color-orange-400);
  --color-info: var(--color-gray-300);
  --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
  --color-btn-primary-text: var(--color-slate-900);
  --color-card-border: rgba(var(--color-gray-400-rgb), 0.15);
  --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, var(--color-background) 0%, rgba(var(--color-purple-500-rgb), 0.05) 100%);
  min-height: 100vh;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-indigo-500) 100%);
  color: var(--color-btn-primary-text);
  box-shadow: 0 4px 6px -1px rgba(var(--color-purple-500-rgb), 0.25);
  transition: all var(--duration-normal) var(--ease-standard);
}

.btn--primary:hover {
  background: linear-gradient(135deg, var(--color-primary-hover) 0%, var(--color-indigo-600) 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(var(--color-purple-500-rgb), 0.3);
}

.btn--primary:active {
  background: linear-gradient(135deg, var(--color-primary-active) 0%, var(--color-indigo-600) 100%);
  transform: translateY(0);
  box-shadow: 0 2px 4px -1px rgba(var(--color-purple-500-rgb), 0.2);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Application Layout */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

.app-header {
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-purple-500-rgb), 0.08) 100%);
  padding: var(--space-24) var(--space-24);
  border-bottom: 1px solid var(--color-border);
  text-align: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(var(--color-purple-500-rgb), 0.1);
}

.app-header h1 {
  font-size: var(--font-size-4xl);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-indigo-500) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-bold);
  letter-spacing: var(--letter-spacing-tight);
}

.app-header p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  margin: 0;
}

.main-content {
  display: flex;
  flex: 1;
  gap: var(--space-16);
  padding: var(--space-16);
}

/* Sidebar */
.sidebar {
  width: 250px;
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  border: 1px solid var(--color-border);
  height: fit-content;
}

.algorithm-nav h3 {
  color: var(--color-text);
  margin-bottom: var(--space-16);
  font-size: var(--font-size-xl);
}

.category-section {
  margin-bottom: var(--space-24);
}

.category-section h4 {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-12);
  font-size: var(--font-size-base);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.algorithm-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.algorithm-btn {
  padding: var(--space-10) var(--space-12);
  background: transparent;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  color: var(--color-text);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  font-size: var(--font-size-sm);
  text-align: left;
}

.algorithm-btn:hover {
  background: linear-gradient(135deg, var(--color-secondary) 0%, rgba(var(--color-purple-500-rgb), 0.08) 100%);
  border-color: var(--color-primary);
  transform: translateX(2px);
}

.algorithm-btn.active {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-indigo-500) 100%);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
  box-shadow: 0 2px 4px -1px rgba(var(--color-purple-500-rgb), 0.25);
}

/* Content Area */
.content-area {
  flex: 1;
  display: grid;
  grid-template-columns: 60% 40%;
  gap: var(--space-16);
}

/* Visualization Section */
.visualization-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.algorithm-info h2 {
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.algorithm-info p {
  color: var(--color-text-secondary);
  margin: 0;
}

/* Input Controls */
.input-controls {
  background-color: var(--color-bg-1);
  padding: var(--space-16);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-border);
}

.control-group {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  margin-bottom: var(--space-12);
}

.control-group label {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  min-width: 100px;
  font-size: var(--font-size-sm);
}

.control-group input[type="range"] {
  flex: 1;
}

.control-group input[type="text"],
.control-group input[type="number"] {
  flex: 1;
  max-width: 200px;
}

#array-size-value, #speed-value {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  min-width: 30px;
}

.control-buttons {
  display: flex;
  gap: var(--space-8);
  margin-top: var(--space-8);
}

.control-buttons .btn {
  padding: var(--space-6) var(--space-12);
  font-size: var(--font-size-sm);
}

/* Animation Controls */
.animation-controls {
  background-color: var(--color-bg-2);
  padding: var(--space-16);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.playback-controls {
  display: flex;
  gap: var(--space-8);
  justify-content: center;
}

.playback-controls .btn {
  padding: var(--space-8) var(--space-16);
  font-size: var(--font-size-sm);
  min-width: 80px;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  justify-content: center;
}

.speed-control input[type="range"] {
  width: 120px;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Visualization Canvas */
.visualization-canvas {
  flex: 1;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-16);
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Array Visualization */
.array-container {
  display: flex;
  align-items: flex-end;
  gap: var(--space-4);
  height: 250px;
  padding: var(--space-16);
}

.array-bar {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-indigo-500) 100%);
  border-radius: var(--radius-sm);
  min-width: 20px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  color: var(--color-btn-primary-text);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-xs);
  padding: var(--space-4);
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
  box-shadow: 0 2px 4px -1px rgba(var(--color-purple-500-rgb), 0.2);
}

.array-bar.comparing {
  background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-orange-400) 100%);
  box-shadow: 0 4px 6px -1px rgba(var(--color-warning-rgb), 0.3);
}

.array-bar.active {
  background: linear-gradient(135deg, var(--color-error) 0%, var(--color-red-400) 100%);
  transform: translateY(-5px);
  box-shadow: 0 6px 8px -1px rgba(var(--color-error-rgb), 0.4);
}

.array-bar.sorted {
  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-green-400) 100%);
  box-shadow: 0 4px 6px -1px rgba(var(--color-success-rgb), 0.3);
}

.array-bar.found {
  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-green-400) 100%);
  transform: scale(1.1);
  box-shadow: 0 6px 8px -1px rgba(var(--color-success-rgb), 0.4);
}

.array-bar.searching {
  background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-orange-400) 100%);
  transform: scale(1.05);
  box-shadow: 0 4px 6px -1px rgba(var(--color-warning-rgb), 0.3);
}

/* Graph Visualization */
.graph-container {
  width: 100%;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.graph-node {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  position: absolute;
  transition: all var(--duration-normal) var(--ease-standard);
}

.graph-node.visited {
  background-color: var(--color-success);
}

.graph-node.current {
  background-color: var(--color-error);
  transform: scale(1.2);
}

.graph-edge {
  position: absolute;
  background-color: var(--color-text-secondary);
  height: 2px;
  transform-origin: left center;
}

.graph-edge.active {
  background-color: var(--color-primary);
  height: 3px;
}

/* Code Section */
.code-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
}

.code-section h3 {
  color: var(--color-text);
  margin-bottom: var(--space-16);
}

.code-container {
  flex: 1;
  background-color: var(--color-background);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-border);
  overflow: auto;
  max-height: 600px;
}

#code-display {
  margin: 0;
  padding: var(--space-16);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  color: var(--color-text);
  background: transparent;
  border: none;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.code-line {
  display: block;
  padding: var(--space-2) 0;
  border-radius: var(--radius-sm);
}

.code-line.highlight {
  background-color: var(--color-bg-4);
  padding-left: var(--space-8);
  padding-right: var(--space-8);
  margin: 0 -var(--space-8);
}

/* Complexity Panel */
.complexity-panel {
  background-color: var(--color-surface);
  border-top: 1px solid var(--color-border);
  padding: var(--space-20);
}

.complexity-info {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: var(--space-24);
  max-width: 1200px;
  margin: 0 auto;
}

.complexity-text h3 {
  color: var(--color-text);
  margin-bottom: var(--space-16);
}

.complexity-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.time-complexity, .space-complexity {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.time-complexity h4, .space-complexity h4 {
  color: var(--color-text);
  font-size: var(--font-size-base);
}

.complexity-cases {
  display: flex;
  gap: var(--space-16);
}

.complexity-case {
  padding: var(--space-6) var(--space-12);
  background-color: var(--color-bg-3);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.complexity-case span {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

#space-complexity {
  padding: var(--space-6) var(--space-12);
  background-color: var(--color-bg-5);
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  display: inline-block;
}

.complexity-explanation {
  padding: var(--space-12);
  background-color: var(--color-bg-1);
  border-radius: var(--radius-base);
  border-left: 4px solid var(--color-primary);
}

.complexity-explanation p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.6;
}

.complexity-chart h4 {
  color: var(--color-text);
  margin-bottom: var(--space-12);
  font-size: var(--font-size-base);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
  }
  
  .content-area {
    grid-template-columns: 1fr;
  }
  
  .complexity-info {
    grid-template-columns: 1fr;
  }
  
  .complexity-cases {
    flex-wrap: wrap;
  }
  
  .array-bar {
    min-width: 15px;
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: var(--space-12) var(--space-16);
  }
  
  .main-content {
    padding: var(--space-8);
    gap: var(--space-8);
  }
  
  .visualization-section, .code-section {
    padding: var(--space-12);
  }
  
  .playback-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .playback-controls .btn {
    min-width: 60px;
    padding: var(--space-6) var(--space-8);
  }
}